<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Tab</title>
    <link rel="stylesheet" href="style.css">
    <script src="config.js"></script>
</head>
<body class="app-loading">
    <!-- 背景层 -->
    <div class="background-layer" id="backgroundLayer"></div>
    
    <!-- 左下角设置按钮 -->
    <div class="sidebar">
        <div class="tool-icon" id="settingsBtn" title="设置">⚙️</div>
    </div>
    
    <!-- 时间显示 -->
    <div class="time-display" id="timeDisplay">
        <div class="time" id="currentTime">15:30</div>
        <div class="date" id="currentDate">2025年1月18日 星期六</div>
    </div>
    
    <!-- 搜索容器 -->
    <div class="search-container">
        <div class="search-box">
            <div class="platform-selector" id="platformSelector">
                <div class="platform-icon-display" id="platformIconDisplay">
                    <img class="platform-favicon" id="platformFavicon" src="" alt="" style="display: none;">
                    <span class="platform-emoji" id="platformEmoji">🌐</span>
                </div>
            </div>
            <input type="text" id="searchInput" placeholder="在当前平台中搜索..." autocomplete="off">
        </div>

        <!-- 平台选择弹出框 -->
        <div class="platform-dropdown" id="platformDropdown" style="display: none;">
            <div class="platform-dropdown-content">
                <div class="platform-search">
                    <input type="text" id="platformSearchInput" placeholder="搜索平台..." autocomplete="off">
                </div>
                <div class="platform-list" id="platformList">
                    <!-- 动态生成平台列表 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 双模式页面管理侧边栏 -->
    <div class="page-sidebar" id="pageSidebar">
        <!-- 常规搜索区域 -->
        <div class="page-section" id="normalPageSection">
            <div class="page-section-header">
                <div class="page-action-btn" id="addNormalPageBtn" title="添加常规搜索页面">+</div>
            </div>
            <div class="page-list" id="normalPageList">
                <!-- 动态生成常规搜索页面图标 -->
            </div>
        </div>

        <!-- 分隔线 -->
        <div class="page-divider"></div>

        <!-- 快捷搜索区域 -->
        <div class="page-section" id="quickPageSection">
            <div class="page-list" id="quickPageList">
                <!-- 动态生成快捷搜索页面图标 -->
            </div>
            <div class="page-section-footer">
                <div class="page-action-btn" id="addQuickPageBtn" title="添加快捷搜索页面">+</div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 快捷方式网格 -->
        <div class="shortcuts-grid" id="shortcutsGrid">
            <!-- 动态生成快捷方式图标 -->
        </div>

        <!-- 页面指示器 -->
        <div class="page-indicator" id="pageIndicator">
            <span class="current-page" id="currentPageName">默认页面</span>
            <span class="page-counter" id="pageCounter">1 / 1</span>
        </div>
    </div>
    
    <!-- 设置面板背景遮罩 -->
    <div class="settings-overlay" id="settingsOverlay"></div>

    <!-- 设置面板 -->
    <div class="settings-panel" id="settingsPanel">
        <div class="settings-header">
            <h3>设置</h3>
            <button class="close-btn" id="closeSettings">×</button>
        </div>
        <div class="settings-content" id="settingsContent">
            <!-- 动态生成设置内容 -->
        </div>
    </div>
    
    <!-- 快捷方式编辑模态框 -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加图标</h3>
                <button class="close-btn" id="closeModal">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="shortcutUrl">URL:</label>
                    <div class="url-input-group">
                        <input type="text" id="shortcutUrl" placeholder="请输入网址">
                        <button type="button" class="btn-fetch-icon" id="fetchIconBtn">获取图标</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="shortcutIcon">图标:</label>
                    <div class="icon-display-group">
                        <div class="icon-preview" id="iconPreview">
                            <img class="preview-favicon" id="previewFavicon" src="" alt="" style="display: none;">
                            <span class="preview-emoji" id="previewEmoji">🌐</span>
                        </div>
                        <div class="icon-status" id="iconStatus">自动获取中...</div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="shortcutName">名称:</label>
                    <input type="text" id="shortcutName" placeholder="自动解析中...">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" id="cancelBtn">取消</button>
                <button class="btn-confirm" id="confirmBtn">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 页面编辑模态框 -->
    <div class="modal-overlay" id="pageModalOverlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="pageModalTitle">添加页面</h3>
                <button class="close-btn" id="closePageModal">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="pageIcon">选择图标:</label>
                    <div class="icon-grid-container">
                        <div class="icon-grid" id="iconGrid">
                            <div class="icon-option selected" data-icon="📄">📄</div>
                            <div class="icon-option" data-icon="🏠">🏠</div>
                            <div class="icon-option" data-icon="⚙️">⚙️</div>
                            <div class="icon-option" data-icon="🔍">🔍</div>
                            <div class="icon-option" data-icon="📊">📊</div>
                            <div class="icon-option" data-icon="📝">📝</div>
                            <div class="icon-option" data-icon="📁">📁</div>
                            <div class="icon-option" data-icon="💼">💼</div>
                            <div class="icon-option" data-icon="🎯">🎯</div>
                            <div class="icon-option" data-icon="📈">📈</div>
                            <div class="icon-option" data-icon="🔧">🔧</div>
                            <div class="icon-option" data-icon="📋">📋</div>
                            <div class="icon-option" data-icon="🌟">🌟</div>
                            <div class="icon-option" data-icon="💡">💡</div>
                            <div class="icon-option" data-icon="🎨">🎨</div>
                            <div class="icon-option" data-icon="📚">📚</div>
                            <div class="icon-option" data-icon="🔒">🔒</div>
                            <div class="icon-option" data-icon="🌐">🌐</div>
                            <div class="icon-option" data-icon="📱">📱</div>
                            <div class="icon-option" data-icon="💻">💻</div>
                            <div class="icon-option" data-icon="🎮">🎮</div>
                            <div class="icon-option" data-icon="🎵">🎵</div>
                            <div class="icon-option" data-icon="📺">📺</div>
                            <div class="icon-option" data-icon="📷">📷</div>
                            <div class="icon-option" data-icon="🛒">🛒</div>
                            <div class="icon-option" data-icon="💳">💳</div>
                            <div class="icon-option" data-icon="🏦">🏦</div>
                            <div class="icon-option" data-icon="🏥">🏥</div>
                            <div class="icon-option" data-icon="🏫">🏫</div>
                            <div class="icon-option" data-icon="🚗">🚗</div>
                            <div class="icon-option" data-icon="✈️">✈️</div>
                            <div class="icon-option" data-icon="🍕">🍕</div>
                            <div class="icon-option" data-icon="☕">☕</div>
                            <div class="icon-option" data-icon="🎪">🎪</div>
                            <div class="icon-option" data-icon="🎭">🎭</div>
                            <div class="icon-option" data-icon="🏃">🏃</div>
                            <div class="icon-option" data-icon="💪">💪</div>
                            <div class="icon-option" data-icon="🧠">🧠</div>
                            <div class="icon-option" data-icon="❤️">❤️</div>
                            <div class="icon-option" data-icon="🔥">🔥</div>
                            <div class="icon-option" data-icon="⭐">⭐</div>
                            <div class="icon-option" data-icon="🌙">🌙</div>
                            <div class="icon-option" data-icon="☀️">☀️</div>
                            <div class="icon-option" data-icon="🌈">🌈</div>
                            <div class="icon-option" data-icon="🎉">🎉</div>
                            <div class="icon-option" data-icon="🎁">🎁</div>
                            <div class="icon-option" data-icon="📧">📧</div>
                            <div class="icon-option" data-icon="💬">💬</div>
                            <div class="icon-option" data-icon="📞">📞</div>
                        </div>
                    </div>
                    <input type="hidden" id="pageIcon" value="📄">
                </div>
                <div class="form-group">
                    <label for="pageName">页面名称:</label>
                    <input type="text" id="pageName" placeholder="请输入页面名称">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" id="cancelPageBtn">取消</button>
                <button class="btn-confirm" id="confirmPageBtn">确定</button>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu" style="display: none;">
        <div class="menu-item" id="editShortcut">编辑</div>
        <div class="menu-item" id="deleteShortcut">删除</div>
    </div>

    <!-- 页面右键菜单 -->
    <div class="context-menu" id="pageContextMenu" style="display: none;">
        <div class="menu-item" id="editPage">编辑页面</div>
        <div class="menu-item" id="deletePage">删除页面</div>
    </div>

    <!-- 数据管理脚本 -->
    <script src="js/ShortcutConfig.js"></script>
    <script src="js/IconDownloader.js"></script>
    <script src="js/ConfigManager.js"></script>
    <script src="js/SearchPlatformConfig.js"></script>
    <script src="js/IconCacheManager.js"></script>
    <script src="js/DataStorageManager.js"></script>
    <script src="js/DesktopDragManager.js"></script>

    <!-- 添加平台模态弹窗 -->
    <div class="add-platform-modal" id="addPlatformModal" style="display: none;">
        <div class="add-platform-overlay"></div>
        <div class="add-platform-content">
            <div class="add-platform-header">
                <h3>添加搜索引擎</h3>
                <button class="add-platform-close" id="addPlatformClose">×</button>
            </div>
            <div class="add-platform-body">
                <p class="add-platform-description">选择要添加的搜索引擎</p>
                <div class="add-platform-grid" id="addPlatformGrid">
                    <!-- 动态生成平台选项 -->
                </div>
            </div>
            <!-- 移除底部按钮，操作立即生效 -->
        </div>
    </div>

    <!-- 主要脚本 -->
    <script src="js/FolderManager.js"></script>
    <script src="script.js"></script>
</body>
</html>
