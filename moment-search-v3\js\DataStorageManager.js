/**
 * Moment Search 数据存储管理器
 * 参考 itab 的数据结构设计，实现完整的数据导入导出功能
 */

class DataStorageManager {
    constructor() {
        this.storageKey = 'moment-search-data';
        this.backupKey = 'moment-search-backup';
        this.maxBackups = 5;
        this.version = '3.0.0';
        
        // 数据结构版本控制
        this.dataVersion = {
            current: '3.0.0',
            compatible: ['2.0.0', '3.0.0']
        };
    }

    /**
     * 获取完整的应用数据
     * @returns {Object} 完整的应用配置数据
     */
    getCompleteData() {
        return {
            // 基础配置
            baseConfig: this.getBaseConfig(),
            
            // 页面和图标配置
            navConfig: this.getNavConfig(),
            
            // 元数据
            metadata: {
                version: this.version,
                exportTime: new Date().toISOString(),
                userAgent: navigator.userAgent,
                timestamp: Date.now()
            }
        };
    }

    /**
     * 获取基础配置
     * @returns {Object} 基础配置对象
     */
    getBaseConfig() {
        const settingsManager = window.app?.settingsManager;
        const searchManager = window.app?.searchManager;
        
        return {
            // 语言设置
            lang: 'zh-CN',
            
            // 搜索引擎配置
            searchEngine: this.getSearchEngineConfig(),
            useSearch: searchManager?.currentPlatform || 'all',
            
            // 搜索框配置
            search: {
                show: true,
                history: false,
                height: 60,
                radius: 23,
                bgColor: 0.8,
                translate: '',
                translateShow: false,
                translateHide: false
            },
            
            // 主题配置
            theme: {
                mode: settingsManager?.settings?.theme?.mode || 'light',
                system: settingsManager?.settings?.theme?.system || true,
                color: settingsManager?.settings?.theme?.color || '#1681ff'
            },
            
            // 侧边栏配置
            sidebar: {
                placement: 'left',
                autoHide: false,
                width: 42,
                lastGroup: false,
                mouseGroup: true,
                opacity: 0.29
            },
            
            // 壁纸配置
            wallpaper: {
                mask: 0,
                blur: 0,
                type: 1,
                src: '',
                thumb: '',
                time: 0,
                source: '',
                switchBtn: true
            },
            
            // 布局配置
            layout: {
                view: 'widget',
                yiyan: false
            },
            
            // 时间显示配置
            time: {
                weekBegin1: false,
                show: true,
                size: 73,
                color: '#fff',
                fontWeight: '400',
                font: 'HarmonyOS_Sans',
                hour24: true,
                sec: true,
                month: 'inline',
                week: 'inline',
                lunar: 'none'
            },
            
            // 打开方式配置
            open: {
                searchBlank: true,
                iconBlank: true
            },
            
            // 图标配置
            icon: {
                name: 1,
                nameSize: 14,
                nameColor: '#fff',
                iconRadius: 34,
                iconSize: 68,
                iconX: 29,
                iconY: 29,
                xysync: true,
                opacity: 1,
                unit: 'px',
                width: 1344,
                iconLayout: 'custom'
            },
            
            // 热门搜索
            topSearch: []
        };
    }

    /**
     * 获取搜索引擎配置
     * @returns {Array} 搜索引擎列表
     */
    getSearchEngineConfig() {
        const searchManager = window.app?.searchManager;
        if (!searchManager) return [];
        
        return searchManager.platforms.map(platform => ({
            key: platform.id,
            title: platform.name,
            href: platform.url
        }));
    }

    /**
     * 获取导航配置（页面和图标数据）
     * @returns {Array} 页面配置数组
     */
    getNavConfig() {
        const pageManager = window.app?.pageManager;
        if (!pageManager) return [];

        return pageManager.pages.map(page => ({
            id: page.id,
            name: page.name,
            icon: page.icon || 'home',
            mode: page.mode || 'normal', // 添加模式标识：normal(常规搜索) / quick(快捷搜索)
            searchMode: page.searchMode || 'direct', // 搜索模式：direct(直接跳转) / search(搜索跳转)
            children: this.convertShortcutsToNavFormat(page.shortcuts || [], page.mode)
        }));
    }

    /**
     * 将快捷方式转换为导航格式
     * @param {Array} shortcuts 快捷方式数组
     * @param {string} pageMode 页面模式：normal(常规搜索) / quick(快捷搜索)
     * @returns {Array} 转换后的图标数组
     */
    convertShortcutsToNavFormat(shortcuts, pageMode = 'normal') {
        return shortcuts.map(shortcut => {
            // 根据页面模式确定URL结构
            const urlConfig = this.getUrlConfig(shortcut, pageMode);

            return {
                id: shortcut.id.toString(),
                url: urlConfig.displayUrl, // 用于显示和图标获取的URL
                searchUrl: urlConfig.searchUrl, // 用于搜索的URL模板
                type: shortcut.type || 'icon',
                name: shortcut.name,
                src: shortcut.src || shortcut.faviconUrl || '', // 优先使用src字段（参考itabdate.md格式）
                backgroundColor: shortcut.backgroundColor || this.generateBackgroundColor(shortcut.name),
                iconText: shortcut.iconText || shortcut.icon || '',
                view: shortcut.view || 0,
                order: shortcut.order || 0,
                category: shortcut.category || 'default',

                // 搜索模式相关字段
                searchMode: urlConfig.searchMode, // direct(直接跳转) / search(搜索跳转)
                pageMode: pageMode, // normal / quick

                // 扩展字段
                emoji: shortcut.icon, // 保留emoji作为备用
                faviconCacheTime: shortcut.faviconCacheTime || null,
                size: shortcut.size || '1x1',

                // 如果是文件夹类型，递归处理子项
                ...(shortcut.children && {
                    children: this.convertShortcutsToNavFormat(shortcut.children, pageMode)
                })
            };
        });
    }

    /**
     * 根据页面模式获取URL配置
     * @param {Object} shortcut 快捷方式对象
     * @param {string} pageMode 页面模式
     * @returns {Object} URL配置对象
     */
    getUrlConfig(shortcut, pageMode) {
        if (pageMode === 'quick' && shortcut.searchUrl) {
            // 快捷搜索模式：使用搜索URL模板
            return {
                displayUrl: shortcut.url, // 用于显示和获取图标的基础URL
                searchUrl: shortcut.searchUrl, // 搜索URL模板，包含{keyword}占位符
                searchMode: 'search'
            };
        } else {
            // 常规搜索模式：直接跳转
            return {
                displayUrl: shortcut.url,
                searchUrl: shortcut.url,
                searchMode: 'direct'
            };
        }
    }

    /**
     * 根据名称生成背景色
     * @param {string} name 图标名称
     * @returns {string} 十六进制颜色值
     */
    generateBackgroundColor(name) {
        const colors = [
            '#1681ff', '#ff4757', '#2ed573', '#ffa502',
            '#3742fa', '#f368e0', '#ff6348', '#70a1ff',
            '#5352ed', '#ff4757', '#2ed573', '#ffa502'
        ];
        
        let hash = 0;
        for (let i = 0; i < name.length; i++) {
            hash = name.charCodeAt(i) + ((hash << 5) - hash);
        }
        
        return colors[Math.abs(hash) % colors.length];
    }

    /**
     * 导出数据
     * @param {string} format 导出格式 ('json' | 'file')
     * @returns {string|void} JSON字符串或触发文件下载
     */
    exportData(format = 'file') {
        try {
            const data = this.getCompleteData();
            const jsonString = JSON.stringify(data, null, 2);
            
            if (format === 'json') {
                return jsonString;
            }
            
            // 下载文件
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `moment-search-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            return true;
        } catch (error) {
            console.error('导出数据失败:', error);
            return false;
        }
    }

    /**
     * 导入数据
     * @param {Object} data 导入的数据对象
     * @returns {boolean} 导入是否成功
     */
    async importData(data) {
        try {
            // 验证数据格式
            if (!this.validateImportData(data)) {
                throw new Error('数据格式无效');
            }
            
            // 创建备份
            await this.createBackup();
            
            // 导入基础配置
            if (data.baseConfig) {
                await this.importBaseConfig(data.baseConfig);
            }
            
            // 导入页面和图标配置
            if (data.navConfig) {
                await this.importNavConfig(data.navConfig);
            }
            
            // 保存导入时间
            localStorage.setItem(`${this.storageKey}_import_time`, Date.now().toString());
            
            // 刷新应用
            if (window.app) {
                window.app.init();
            }
            
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            // 尝试恢复备份
            await this.restoreFromBackup();
            return false;
        }
    }

    /**
     * 验证导入数据
     * @param {Object} data 待验证的数据
     * @returns {boolean} 数据是否有效
     */
    validateImportData(data) {
        // 基本结构检查
        if (!data || typeof data !== 'object') {
            return false;
        }
        
        // 版本兼容性检查
        if (data.metadata?.version && 
            !this.dataVersion.compatible.includes(data.metadata.version)) {
            console.warn('数据版本不兼容:', data.metadata.version);
        }
        
        // 必要字段检查
        return data.baseConfig || data.navConfig || data.shortcuts;
    }

    /**
     * 导入基础配置
     * @param {Object} baseConfig 基础配置对象
     */
    async importBaseConfig(baseConfig) {
        const settingsManager = window.app?.settingsManager;
        if (!settingsManager) return;
        
        // 更新设置
        if (baseConfig.theme) {
            settingsManager.updateSettings('theme', baseConfig.theme);
        }
        
        if (baseConfig.icon) {
            settingsManager.updateSettings('icon', baseConfig.icon);
        }
        
        // 应用设置
        settingsManager.applySettings();
    }

    /**
     * 导入页面和图标配置
     * @param {Array} navConfig 页面配置数组
     */
    async importNavConfig(navConfig) {
        const pageManager = window.app?.pageManager;
        if (!pageManager) return;
        
        // 清空现有页面
        pageManager.pages = [];
        
        // 导入页面数据
        for (const pageData of navConfig) {
            const shortcuts = this.convertNavFormatToShortcuts(pageData.children || []);
            
            pageManager.addPage({
                name: pageData.name,
                icon: pageData.icon,
                shortcuts: shortcuts
            });
        }
        
        // 保存并渲染
        pageManager.savePages();
        pageManager.render();
    }

    /**
     * 将导航格式转换为快捷方式
     * @param {Array} navItems 导航项数组
     * @param {string} pageMode 页面模式
     * @returns {Array} 快捷方式数组
     */
    convertNavFormatToShortcuts(navItems, pageMode = 'normal') {
        return navItems.map(item => {
            const shortcut = {
                id: parseInt(item.id) || Date.now(),
                name: item.name,
                url: item.url, // 基础URL，用于显示和图标获取
                icon: item.iconText || item.emoji || '🌐',
                src: item.src, // 使用src字段保存图标URL（参考itabdate.md格式）
                faviconUrl: item.src, // 兼容旧版本
                faviconCacheTime: item.faviconCacheTime || Date.now(),
                backgroundColor: item.backgroundColor,
                order: item.order || 0,
                category: item.category || 'default',
                view: item.view || 0,
                type: item.type || 'icon',
                size: item.size || '1x1',

                // 搜索模式相关字段
                searchMode: item.searchMode || 'direct',
                pageMode: item.pageMode || pageMode,

                // 如果有子项，递归处理
                ...(item.children && {
                    children: this.convertNavFormatToShortcuts(item.children, pageMode)
                })
            };

            // 如果是快捷搜索模式，添加搜索URL
            if (item.searchUrl && item.searchMode === 'search') {
                shortcut.searchUrl = item.searchUrl;
            }

            return shortcut;
        });
    }

    /**
     * 创建备份
     */
    async createBackup() {
        try {
            const currentData = this.getCompleteData();
            const backups = this.getBackups();
            
            backups.unshift({
                timestamp: Date.now(),
                data: currentData,
                version: this.version
            });
            
            // 保持最大备份数量
            if (backups.length > this.maxBackups) {
                backups.splice(this.maxBackups);
            }
            
            localStorage.setItem(this.backupKey, JSON.stringify(backups));
        } catch (error) {
            console.error('创建备份失败:', error);
        }
    }

    /**
     * 获取备份列表
     * @returns {Array} 备份数组
     */
    getBackups() {
        try {
            const backups = localStorage.getItem(this.backupKey);
            return backups ? JSON.parse(backups) : [];
        } catch (error) {
            console.error('获取备份失败:', error);
            return [];
        }
    }

    /**
     * 从备份恢复
     * @param {number} backupIndex 备份索引，默认为最新备份
     */
    async restoreFromBackup(backupIndex = 0) {
        try {
            const backups = this.getBackups();
            if (backups.length === 0 || !backups[backupIndex]) {
                throw new Error('没有可用的备份');
            }
            
            const backup = backups[backupIndex];
            await this.importData(backup.data);
            
            console.log('从备份恢复成功:', new Date(backup.timestamp));
        } catch (error) {
            console.error('从备份恢复失败:', error);
        }
    }

    /**
     * 清理过期备份
     * @param {number} maxAge 最大保存天数，默认30天
     */
    cleanupBackups(maxAge = 30) {
        try {
            const backups = this.getBackups();
            const cutoffTime = Date.now() - (maxAge * 24 * 60 * 60 * 1000);
            
            const validBackups = backups.filter(backup => backup.timestamp > cutoffTime);
            
            localStorage.setItem(this.backupKey, JSON.stringify(validBackups));
        } catch (error) {
            console.error('清理备份失败:', error);
        }
    }
}

// 导出类
window.DataStorageManager = DataStorageManager;
